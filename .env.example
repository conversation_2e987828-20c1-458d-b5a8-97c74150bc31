# Added by Payload
PREVIEW_SECRET=YOUR_SECRET_HERE
# Used to validate preview requests
CRON_SECRET=YOUR_CRON_SECRET_HERE
# Secret used to authenticate cron jobs
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
# Used to configure CORS, format links and more. No trailing slash
PAYLOAD_SECRET=e30be318c9947d5f7c45d6fa
# Used to encrypt JWT tokens
#DATABASE_URI=postgresql://127.0.0.1:5432/your-database-name
# Or use a PG connection string
DATABASE_URI=mongodb://127.0.0.1/
# Database connection string

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret

# Stripe Configuration
STRIPE_PUBLIC_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Shiprocket Configuration
SHIPROCKET_EMAIL=your-shiprocket-email
SHIPROCKET_PASSWORD=your-shiprocket-password

# AWS S3 Configuration (for cloud storage)
# Option 1: Use AWS credentials directly
S3_ACCESS_KEY_ID=your-s3-access-key-id
S3_SECRET_ACCESS_KEY=your-s3-secret-access-key
S3_REGION=your-s3-region
S3_BUCKET=your-s3-bucket

# Option 2: Use AWS profile (recommended for local development)
AWS_PROFILE=harshluhar

# Optional: Custom S3 endpoint (for S3-compatible services)
S3_ENDPOINT=your-s3-endpoint