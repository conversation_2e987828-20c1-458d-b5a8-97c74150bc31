# AWS S3 Configuration for <PERSON> Tees B2B Platform
# Copy this file to .env and update with your actual values

# Database Configuration
DATABASE_URI=mongodb://localhost:27017/maddox-tees
PAYLOAD_SECRET=your-payload-secret-key

# NextAuth Configuration
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000

# AWS S3 Configuration
# Option 1: Use AWS Profile (Recommended for local development)
AWS_PROFILE=harshluhar

# Option 2: Use explicit AWS credentials (for production)
# S3_ACCESS_KEY_ID=your-access-key-id
# S3_SECRET_ACCESS_KEY=your-secret-access-key

# S3 Bucket Configuration
S3_REGION=us-east-1
S3_BUCKET=maddox-tees-media

# Optional: Custom S3 endpoint (for S3-compatible services like DigitalOcean Spaces)
# S3_ENDPOINT=https://nyc3.digitaloceanspaces.com

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key

# Shiprocket Configuration
SHIPROCKET_EMAIL=your-shiprocket-email
SHIPROCKET_PASSWORD=your-shiprocket-password

# Application URLs
NEXT_PUBLIC_SERVER_URL=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=image/jpeg,image/jpg,image/png,image/svg+xml,application/pdf,image/webp

# AWS S3 Folder Structure
# The following folders will be created automatically in your S3 bucket:
# - uploads/          (general file uploads)
# - dtf-designs/      (DTF sticker design files)
# - custom-designs/   (custom t-shirt design files)
# - mockups/          (generated mockup images)
# - products/         (product images)
# - profiles/         (user profile images)

# AWS IAM Permissions Required for S3 Bucket:
# {
#   "Version": "2012-10-17",
#   "Statement": [
#     {
#       "Effect": "Allow",
#       "Action": [
#         "s3:GetObject",
#         "s3:PutObject",
#         "s3:DeleteObject",
#         "s3:ListBucket"
#       ],
#       "Resource": [
#         "arn:aws:s3:::maddox-tees-media",
#         "arn:aws:s3:::maddox-tees-media/*"
#       ]
#     }
#   ]
# }

# S3 Bucket CORS Configuration:
# [
#   {
#     "AllowedHeaders": ["*"],
#     "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
#     "AllowedOrigins": ["http://localhost:3000", "https://yourdomain.com"],
#     "ExposeHeaders": ["ETag"]
#   }
# ]

# S3 Bucket Policy (for public read access to uploaded files):
# {
#   "Version": "2012-10-17",
#   "Statement": [
#     {
#       "Sid": "PublicReadGetObject",
#       "Effect": "Allow",
#       "Principal": "*",
#       "Action": "s3:GetObject",
#       "Resource": "arn:aws:s3:::maddox-tees-media/*"
#     }
#   ]
# }
