# Maddox Tees B2B Platform - Admin Instructions

## Overview
This document contains comprehensive instructions and rules for managing the Maddox Tees B2B platform. This platform is designed as an admin-only application with customer portal functionality.

## Platform Architecture

### User Roles
1. **Admin Users**: Full access to all platform features and data management
2. **Customer Users**: Limited access to their own orders and profile information

### Access Control Rules
- Only ONE admin user should exist in the system
- New admin user creation is disabled by default
- All new registrations automatically create customer-role users
- Customer users cannot be promoted to admin role without admin intervention

## Data Protection Rules

### Customer Data Protection
1. **Customer Records Cannot Be Deleted**
   - Customer records in the Customers collection are protected from deletion
   - This ensures data integrity and audit compliance
   - Status can be changed to 'inactive' or 'suspended' instead of deletion

2. **Customer User Accounts Cannot Be Deleted**
   - User accounts with 'customer' role are protected from deletion
   - This maintains login history and data relationships
   - Accounts can be deactivated by changing customer status

3. **Order History Preservation**
   - All order records are maintained for audit and business intelligence
   - Customer order history remains accessible even if customer is inactive

### Admin User Management
1. **Single Admin Policy**
   - Only one admin user should exist in the system
   - New admin user creation is disabled to prevent unauthorized access
   - If additional admin access is needed, contact the development team

2. **Admin User Protection**
   - Admin users can be deleted only by other admin users
   - Exercise extreme caution when managing admin accounts

## Customer Registration System

### Registration Process
1. **Public Registration Page**: `/register`
   - Accessible to potential customers without authentication
   - Creates both User and Customer records automatically
   - All registrations default to 'customer' role

2. **Required Information**
   - Email address (must be unique)
   - Password (minimum 8 characters)
   - First and Last Name
   - Company Name
   - Phone Number
   - Complete Billing Address
   - Customer Type (Retail/Wholesale)
   - GSTIN (optional)

3. **Automatic Account Linking**
   - User account is automatically created with customer role
   - Customer record is linked to user account
   - Customer can immediately access their portal after registration

### Customer Portal Access
1. **Login**: Customers use `/admin/login` with their registered credentials
2. **Dashboard**: Custom dashboard showing order history and profile management
3. **Restricted Access**: Customers can only see their own data

## Collection Management Rules

### Customers Collection
- **Create**: Admins can create customers manually OR through registration system
- **Read**: Admins see all customers, customers see only their own record
- **Update**: Admins can update any customer, customers can update their own profile
- **Delete**: DISABLED - Use status field instead

### Users Collection
- **Create**: Admins can create users OR registration system creates customer users
- **Read**: Users can read their own data, admins can read all
- **Update**: Users can update their own profile, admins can update any
- **Delete**: Customer users CANNOT be deleted, admin users can be deleted by admins

### Orders Collection
- **Create**: Only admins can create orders
- **Read**: Admins see all orders, customers see only their own orders
- **Update**: Only admins can update orders
- **Delete**: Only admins can delete orders (use with caution)

## Security Measures

### Authentication
- All admin functions require authentication
- Customer portal requires customer-role authentication
- Session management handled by Payload CMS

### Authorization
- Role-based access control (RBAC) implemented
- Row-level security for customer data
- Admin-only access to sensitive operations

### Data Validation
- Email uniqueness enforced across both Users and Customers collections
- Password strength requirements (minimum 8 characters)
- Required field validation on registration

## Operational Guidelines

### Customer Management
1. **New Customer Onboarding**
   - Direct customers to `/register` for self-service registration
   - Alternatively, create customer records manually in admin panel
   - Verify customer information and approve wholesale accounts if needed

2. **Customer Support**
   - Access customer records through Customers collection
   - View customer order history through Orders collection
   - Update customer information as needed
   - Use 'notes' field for internal customer communication tracking

3. **Account Status Management**
   - Use status field: 'active', 'inactive', 'suspended'
   - Inactive customers cannot log in to portal
   - Suspended customers require admin review before reactivation

### Order Management
1. **Order Creation**
   - Create orders through admin panel
   - Link orders to appropriate customer records
   - Ensure all required fields are completed

2. **Order Tracking**
   - Update order status as orders progress
   - Customers can view order status in their portal
   - Maintain order history for business intelligence

### System Maintenance
1. **Regular Backups**
   - Ensure database backups are performed regularly
   - Test backup restoration procedures

2. **User Account Monitoring**
   - Monitor for suspicious login attempts
   - Review customer registration patterns
   - Maintain audit logs

## Emergency Procedures

### Lost Admin Access
1. Contact development team immediately
2. Do not attempt to create new admin users through registration
3. Database-level intervention may be required

### Data Recovery
1. Customer and order data cannot be deleted through normal operations
2. If data appears missing, check status fields first
3. Contact development team for data recovery assistance

### Security Incidents
1. Immediately change admin passwords
2. Review recent user activity
3. Check for unauthorized data access
4. Contact development team for security audit

## Testing and Verification

### Registration System Testing
A test script is available at `test-customer-registration.js` to verify:
- Customer registration API functionality
- Data protection rules enforcement
- User role management
- Access control verification

To run tests:
```bash
node test-customer-registration.js
```

### Manual Testing Checklist
1. **Registration Flow**
   - [ ] Visit `/register` page
   - [ ] Complete registration form
   - [ ] Verify email uniqueness validation
   - [ ] Confirm automatic customer role assignment
   - [ ] Test login with new credentials

2. **Data Protection**
   - [ ] Attempt to delete customer record (should fail)
   - [ ] Attempt to delete customer user (should fail)
   - [ ] Verify admin can still manage data

3. **Customer Portal**
   - [ ] Customer can access `/admin/login`
   - [ ] Customer sees only their own data
   - [ ] Customer can update their profile
   - [ ] Customer can view their orders

## API Endpoints

### Customer Registration
- **Endpoint**: `POST /api/auth/register`
- **Purpose**: Create new customer accounts
- **Access**: Public (unauthenticated)
- **Validation**: Email uniqueness, required fields, password strength

### Authentication
- **Login**: `POST /api/users/login`
- **Logout**: `POST /api/users/logout`
- **Forgot Password**: `POST /api/users/forgot-password`

## Contact Information

For technical support or system modifications:
- Development Team: [Contact Information]
- Emergency Support: [Emergency Contact]

## Version History

- v1.0: Initial admin instructions with customer registration and data protection features
- v1.1: Added registration system, data protection rules, and testing procedures
- Date: [Current Date]
- Last Updated: [Current Date]

---

**Important**: This document should be reviewed and updated whenever system changes are made. All admin users should be familiar with these instructions and procedures.
