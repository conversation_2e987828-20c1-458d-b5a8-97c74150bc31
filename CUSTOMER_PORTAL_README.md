# Customer Portal Implementation

This document describes the customer portal system implemented for the Maddox Tees B2B platform.

## Overview

The customer portal allows customers to access a restricted view of the admin panel where they can:
- View their order history
- Track order status
- Access read-only order details
- Manage their profile (limited)

## Architecture

### 1. User Role System

**Users Collection** (`src/collections/Users/<USER>
- Added `role` field with options: `admin`, `customer`
- Added `customerId` field to link customer users to customer records
- Implemented role-based access controls

### 2. Access Control Functions

**New Access Control Files:**
- `src/access/adminOnly.ts` - Restricts access to admin role only
- `src/access/customerOnly.ts` - Restricts access to customer role only
- `src/access/adminOrOwner.ts` - Allows admin or data owner access

### 3. Customer-User Integration

**Customers Collection** (`src/collections/Customers.ts`)
- Added `initialPassword` field for setting up customer portal access
- Added `userId` field to link to user accounts
- Implemented hooks for automatic user account creation:
  - `beforeChange` hook creates user account when customer is created
  - `afterChange` hook syncs customer data with user account

### 4. Orders Access Control

**Orders Collection** (`src/collections/Orders.ts`)
- Enhanced access controls for customer role filtering
- Implemented row-level security:
  - Admins can access all orders
  - Customer users can only access orders linked to their customer record
- Read-only permissions for customer users

### 5. Admin Panel Customization

**Custom Components:**
- `src/components/CustomerNav/index.tsx` - Welcome message for customer users
- `src/components/CustomerDashboard/index.tsx` - Custom dashboard for customer portal
- `src/components/AdminNavFilter/index.tsx` - Hides admin collections from customer users

## Usage

### Creating a Customer with Portal Access

1. Navigate to `/admin/collections/customers`
2. Click "Create New"
3. Fill in customer details
4. Set an `initialPassword` for portal access
5. Save the customer

The system will automatically:
- Create a user account with the same email
- Set the user role to "customer"
- Link the user account to the customer record
- Remove the password from customer data for security

### Customer Login Process

1. Customer visits `/admin`
2. Logs in with their email and the password set during customer creation
3. Gets redirected to a restricted admin panel view
4. Can only see and access the Orders collection
5. Can only view orders linked to their customer record

### Admin Management

Admins can:
- Create and manage customer accounts
- View all orders and customers
- Access all admin functionality
- Reset customer passwords through the Users collection

## Security Features

### Authentication & Authorization
- Role-based access control at collection and record level
- Row-level security for customer data isolation
- Secure password handling with automatic cleanup

### Data Protection
- Customers can only access their own orders
- Customer users cannot access other collections
- Admin-only access to customer management
- Read-only permissions for customer users

### UI Security
- Dynamic navigation hiding based on user role
- Custom dashboard for customer users
- Admin-specific features hidden from customers

## File Structure

```
src/
├── access/
│   ├── adminOnly.ts          # Admin-only access control
│   ├── customerOnly.ts       # Customer-only access control
│   └── adminOrOwner.ts       # Admin or owner access control
├── collections/
│   ├── Users/index.ts        # Enhanced with role system
│   ├── Customers.ts          # Auto user creation hooks
│   └── Orders.ts             # Customer access filtering
├── components/
│   ├── CustomerNav/          # Customer portal navigation
│   ├── CustomerDashboard/    # Customer portal dashboard
│   └── AdminNavFilter/       # Navigation filtering
└── scripts/
    └── test-customer-portal.ts # Test script for verification
```

## Testing

Run the test script to verify the implementation:

```bash
npx tsx src/scripts/test-customer-portal.ts
```

The test script will:
1. Create a test customer with user account
2. Verify user account creation and linking
3. Create a test order for the customer
4. Test access controls and data filtering
5. Clean up test data

## Configuration

The customer portal is configured in `src/payload.config.ts`:

```typescript
admin: {
  components: {
    beforeDashboard: ['@/components/BeforeDashboard', '@/components/CustomerDashboard'],
    beforeNavLinks: ['@/components/CustomerNav'],
    afterNavLinks: ['@/components/AdminNavFilter'],
  },
}
```

## Troubleshooting

### Common Issues

1. **Customer can't log in**
   - Verify user account was created (check Users collection)
   - Ensure customer status is "active"
   - Check password was set correctly

2. **Customer sees admin collections**
   - Verify user role is set to "customer"
   - Check if custom components are loaded correctly
   - Regenerate import map: `npm run generate:importmap`

3. **Customer can't see their orders**
   - Verify customerId is linked correctly in user account
   - Check order customer field matches customer record
   - Ensure orders exist for the customer

### Regenerating Types

After making changes to collections:

```bash
npm run generate:types
npm run generate:importmap
```

## Future Enhancements

Potential improvements:
- Password reset functionality for customers
- Email notifications for order updates
- Customer profile editing capabilities
- Order status tracking with timeline
- Invoice download functionality
- Customer-specific pricing and catalogs
