/**
 * <PERSON><PERSON><PERSON> to check users in the database and their roles
 */

import { getPayload } from 'payload'
import config from '@payload-config'

async function checkUsers() {
  console.log('🔍 Checking users in database...\n')

  try {
    const payload = await getPayload({ config })

    // Get all users
    const users = await payload.find({
      collection: 'users',
      limit: 100,
    })

    console.log(`Found ${users.docs.length} users:\n`)

    users.docs.forEach((user, index) => {
      console.log(`${index + 1}. User:`)
      console.log(`   ID: ${user.id}`)
      console.log(`   Email: ${user.email}`)
      console.log(`   Role: ${user.role}`)
      console.log(`   Name: ${user.name || 'N/A'}`)
      console.log(`   Customer ID: ${user.customerId || 'N/A'}`)
      console.log(`   Created: ${user.createdAt}`)
      console.log(`   Updated: ${user.updatedAt}`)
      console.log('')
    })

    // Check for admin users specifically
    const adminUsers = users.docs.filter((user) => user.role === 'admin')
    const customerUsers = users.docs.filter((user) => user.role === 'customer')

    console.log(`📊 Summary:`)
    console.log(`   Admin users: ${adminUsers.length}`)
    console.log(`   Customer users: ${customerUsers.length}`)
    console.log(`   Total users: ${users.docs.length}`)

    if (adminUsers.length === 0) {
      console.log('\n⚠️  WARNING: No admin users found!')
      console.log('   This explains why admin access is not working.')
    }

    if (adminUsers.length > 0) {
      console.log('\n👑 Admin users:')
      adminUsers.forEach((admin) => {
        console.log(`   - ${admin.email} (${admin.name || 'No name'})`)
      })
    }

    if (customerUsers.length > 0) {
      console.log('\n👤 Customer users:')
      customerUsers.forEach((customer) => {
        console.log(`   - ${customer.email} (${customer.name || 'No name'})`)
      })
    }
  } catch (error) {
    console.error('❌ Error checking users:', error.message)
  }
}

checkUsers().catch(console.error)
