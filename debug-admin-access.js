/**
 * Debug script to check admin access and collection visibility
 */

async function checkAdminAccess() {
  console.log('🔍 Debugging Admin Access Issues...\n')

  try {
    // Test 1: Check if we can access the admin API
    console.log('1. Testing admin API access...')
    const meResponse = await fetch('http://localhost:3000/api/users/me', {
      credentials: 'include',
    })
    
    if (meResponse.ok) {
      const userData = await meResponse.json()
      console.log('✅ Current user:', {
        id: userData.user?.id,
        email: userData.user?.email,
        role: userData.user?.role,
        name: userData.user?.name
      })
    } else {
      console.log('❌ Not logged in or session expired')
      return
    }

    // Test 2: Check Customers collection access
    console.log('\n2. Testing Customers collection access...')
    const customersResponse = await fetch('http://localhost:3000/api/customers', {
      credentials: 'include',
    })
    
    if (customersResponse.ok) {
      const customersData = await customersResponse.json()
      console.log('✅ Customers collection accessible')
      console.log(`   Found ${customersData.docs?.length || 0} customer records`)
    } else {
      console.log('❌ Customers collection not accessible')
      console.log('   Status:', customersResponse.status)
      const errorData = await customersResponse.json().catch(() => ({}))
      console.log('   Error:', errorData.message || 'Unknown error')
    }

    // Test 3: Check DTF Pricing global access
    console.log('\n3. Testing DTF Pricing global access...')
    const dtfPricingResponse = await fetch('http://localhost:3000/api/globals/dtf-pricing', {
      credentials: 'include',
    })
    
    if (dtfPricingResponse.ok) {
      const dtfPricingData = await dtfPricingResponse.json()
      console.log('✅ DTF Pricing global accessible')
      console.log('   Config exists:', !!dtfPricingData)
    } else {
      console.log('❌ DTF Pricing global not accessible')
      console.log('   Status:', dtfPricingResponse.status)
      const errorData = await dtfPricingResponse.json().catch(() => ({}))
      console.log('   Error:', errorData.message || 'Unknown error')
    }

    // Test 4: Check other collections for comparison
    console.log('\n4. Testing other collections for comparison...')
    
    const collections = ['products', 'orders', 'users', 'pages', 'posts']
    
    for (const collection of collections) {
      const response = await fetch(`http://localhost:3000/api/${collection}`, {
        credentials: 'include',
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log(`✅ ${collection}: accessible (${data.docs?.length || 0} records)`)
      } else {
        console.log(`❌ ${collection}: not accessible (status: ${response.status})`)
      }
    }

    // Test 5: Check globals
    console.log('\n5. Testing other globals...')
    
    const globals = ['header', 'footer']
    
    for (const global of globals) {
      const response = await fetch(`http://localhost:3000/api/globals/${global}`, {
        credentials: 'include',
      })
      
      if (response.ok) {
        console.log(`✅ ${global}: accessible`)
      } else {
        console.log(`❌ ${global}: not accessible (status: ${response.status})`)
      }
    }

  } catch (error) {
    console.error('❌ Error during debugging:', error.message)
  }
}

// Run the debug check
checkAdminAccess().catch(console.error)
