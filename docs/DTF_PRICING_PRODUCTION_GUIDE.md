# DTF Pricing System - Production Deployment Guide

## 🚀 Production Readiness Checklist

### ✅ Security Features Implemented

- **Rate Limiting**: 100 requests/minute for config, 50 requests/minute for quotes
- **Input Validation**: Comprehensive validation for all inputs
- **Authentication**: Admin-only access with Payload's built-in auth
- **Data Sanitization**: All inputs sanitized before database operations
- **Error Handling**: Secure error responses (no sensitive data leakage)
- **Request Size Limits**: 1MB payload limit to prevent DoS attacks
- **CORS Protection**: Proper headers and origin validation

### ✅ Performance Optimizations

- **Caching**: 5-minute cache for pricing config, 1-minute for quotes
- **Database Optimization**: Efficient queries with proper indexing
- **Response Compression**: Optimized JSON responses
- **Memory Management**: Proper cleanup and garbage collection
- **Connection Pooling**: Efficient database connection handling

### ✅ Error Handling & Monitoring

- **Comprehensive Error Boundaries**: React error boundaries for UI
- **Structured Logging**: Detailed logs with timestamps and user tracking
- **Graceful Degradation**: Fallback mechanisms for service failures
- **Validation Feedback**: Clear error messages for users
- **Audit Trail**: All pricing changes logged with user and timestamp

### ✅ Data Integrity & Validation

- **Schema Validation**: TypeScript interfaces and runtime validation
- **Business Logic Validation**: Overlapping tiers detection
- **Data Consistency**: Atomic operations and transaction handling
- **Backup Strategy**: Versioned global configuration
- **Migration Safety**: Backward compatibility for data structures

## 🔧 Environment Configuration

### Required Environment Variables

```bash
# Database
DATABASE_URI=mongodb://localhost:27017/maddox-tees
PAYLOAD_SECRET=your-super-secret-key

# Security
NODE_ENV=production
PAYLOAD_CONFIG_PATH=./src/payload.config.ts

# AWS S3 (if using cloud storage)
AWS_PROFILE=harshluhar
AWS_REGION=ap-south-1
S3_BUCKET=maddox-tees-media

# Rate Limiting (optional overrides)
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=100
QUOTE_RATE_LIMIT_MAX_REQUESTS=50

# Monitoring (optional)
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info
```

### Production Build Commands

```bash
# Install dependencies
npm ci --only=production

# Build the application
npm run build

# Generate types and import maps
npm run generate:types
npm run generate:importmap

# Start production server
npm start
```

## 📊 Monitoring & Health Checks

### Health Check Endpoints

```bash
# Basic health check
GET /api/health

# DTF Pricing system health
GET /api/dtf-pricing/health

# Database connectivity
GET /api/health/database
```

### Key Metrics to Monitor

1. **Response Times**
   - API response times < 200ms
   - Quote calculations < 500ms
   - Admin interface load < 2s

2. **Error Rates**
   - 4xx errors < 5%
   - 5xx errors < 1%
   - Validation errors tracked

3. **Resource Usage**
   - Memory usage < 80%
   - CPU usage < 70%
   - Database connections < 80% of pool

4. **Business Metrics**
   - Quote calculation success rate > 99%
   - Pricing configuration updates
   - User activity patterns

### Logging Configuration

```javascript
// Production logging setup
const logger = {
  level: process.env.LOG_LEVEL || 'info',
  format: 'json',
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({ format: winston.format.simple() })
  ]
}
```

## 🔒 Security Hardening

### API Security Headers

```javascript
// Add to your Next.js configuration
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
]
```

### Database Security

- Use connection strings with authentication
- Enable MongoDB authentication
- Configure proper user roles and permissions
- Regular security updates
- Network-level access controls

## 🧪 Testing Strategy

### Automated Testing

```bash
# Run production test suite
node scripts/test-dtf-pricing-production.js

# Load testing
npm run test:load

# Security testing
npm run test:security

# Integration testing
npm run test:integration
```

### Manual Testing Checklist

- [ ] Admin login and authentication
- [ ] DTF pricing configuration CRUD operations
- [ ] Size tier management (add, edit, delete)
- [ ] Validation error handling
- [ ] Quote calculation accuracy
- [ ] Rate limiting functionality
- [ ] Error boundary behavior
- [ ] Mobile responsiveness
- [ ] Browser compatibility

## 📈 Performance Benchmarks

### Target Performance Metrics

| Metric | Target | Measurement |
|--------|--------|-------------|
| Page Load Time | < 2s | First Contentful Paint |
| API Response Time | < 200ms | 95th percentile |
| Quote Calculation | < 500ms | Average response time |
| Database Query | < 100ms | Average query time |
| Memory Usage | < 512MB | Peak usage |
| CPU Usage | < 50% | Average load |

### Load Testing Results

```bash
# Expected capacity
- Concurrent users: 100+
- Requests per second: 1000+
- Quote calculations per minute: 500+
- Configuration updates per hour: 100+
```

## 🚨 Incident Response

### Common Issues & Solutions

1. **High Response Times**
   - Check database connection pool
   - Verify cache hit rates
   - Monitor memory usage
   - Scale horizontally if needed

2. **Validation Errors**
   - Check input data format
   - Verify business logic rules
   - Review recent configuration changes
   - Check for data corruption

3. **Authentication Issues**
   - Verify Payload auth configuration
   - Check session management
   - Review user permissions
   - Validate JWT tokens

4. **Quote Calculation Errors**
   - Verify pricing configuration
   - Check size tier overlaps
   - Validate input parameters
   - Review calculation logic

### Emergency Contacts

- **System Administrator**: [<EMAIL>]
- **Database Administrator**: [<EMAIL>]
- **Development Team**: [<EMAIL>]

## 🔄 Backup & Recovery

### Backup Strategy

1. **Database Backups**
   - Daily automated backups
   - Point-in-time recovery capability
   - Cross-region backup storage
   - Monthly backup testing

2. **Configuration Backups**
   - Version control for all configurations
   - Automated configuration snapshots
   - Rollback procedures documented
   - Change tracking enabled

3. **Application Backups**
   - Source code in version control
   - Docker images versioned
   - Environment configurations secured
   - Deployment scripts maintained

### Recovery Procedures

1. **Database Recovery**
   ```bash
   # Restore from backup
   mongorestore --uri="mongodb://localhost:27017/maddox-tees" /path/to/backup
   
   # Verify data integrity
   node scripts/verify-data-integrity.js
   ```

2. **Configuration Recovery**
   ```bash
   # Rollback to previous version
   git checkout <previous-commit>
   npm run deploy
   
   # Verify system functionality
   node scripts/test-dtf-pricing-production.js
   ```

## 📋 Maintenance Schedule

### Daily Tasks
- Monitor system health metrics
- Review error logs
- Check backup completion
- Verify quote calculation accuracy

### Weekly Tasks
- Performance analysis
- Security log review
- Database optimization
- User feedback review

### Monthly Tasks
- Security updates
- Performance benchmarking
- Backup testing
- Documentation updates

### Quarterly Tasks
- Security audit
- Load testing
- Disaster recovery testing
- System architecture review

## 🎯 Success Metrics

### Technical KPIs
- 99.9% uptime
- < 200ms average response time
- < 1% error rate
- 100% quote calculation accuracy

### Business KPIs
- User satisfaction > 95%
- Configuration change success rate > 99%
- Support ticket reduction
- Operational efficiency improvement

## 📞 Support & Maintenance

For production support and maintenance:

- **Documentation**: `/docs/DTF_PRICING_SYSTEM.md`
- **API Reference**: `/docs/api/dtf-pricing.md`
- **Troubleshooting**: `/docs/troubleshooting.md`
- **Change Log**: `/CHANGELOG.md`

---

**Last Updated**: [Current Date]
**Version**: 1.0.0
**Reviewed By**: [Team Lead]
