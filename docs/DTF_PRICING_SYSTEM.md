# DTF Printing Pricing Management System

## Overview

The DTF (Direct-to-Film) Printing Pricing Management System is a comprehensive admin-only solution for configuring and managing pricing for DTF printing services in the Maddox Tees B2B platform.

## Architecture

### Data Storage
- **Payload Global**: `dtf-pricing` - Stores all pricing configuration data
- **TypeScript Interfaces**: Comprehensive type definitions in `src/types/dtf-pricing.ts`
- **API Endpoints**: RESTful API for CRUD operations and quote calculations

### Components
- **DTFPricingAdmin**: Main admin interface component
- **DTFPricingNav**: Navigation integration component
- **Custom Admin Page**: `/admin/dtf-pricing` route

## Features Implemented

### 1. Basic Configuration
- **Pricing Models**: 
  - Per Square Inch
  - Per Linear Inch
  - Flat Rate by Size Category
  - Tiered Pricing
- **Measurement Units**: Inches, Centimeters, Millimeters
- **Currency Support**: INR, USD, EUR
- **System Status**: Enable/disable pricing system

### 2. Base Pricing Settings
- **Minimum Order Value**: Set minimum order requirements
- **Setup Fee**: One-time setup cost per order
- **Rush Order Multiplier**: Premium pricing for expedited orders

### 3. Size-Based Pricing Tiers
- **Tier Configuration**: Name, dimensions (min/max width/height)
- **Flexible Pricing**: Base price + optional price per unit
- **Active/Inactive Status**: Enable/disable specific tiers
- **Dynamic Management**: Add, edit, remove tiers through UI

### 4. Payment Terms Configuration
- **Default Terms**: 100% upfront, 50/50 split, custom split
- **Minimum Advance**: Configurable advance payment percentage

### 5. Admin Interface Features
- **Tabbed Interface**: Organized sections for different configurations
- **Real-time Validation**: Form validation and error handling
- **Auto-save Functionality**: Automatic timestamp and user tracking
- **Responsive Design**: Works on desktop and mobile devices

## Data Structure

### DTF Pricing Global Schema
```typescript
interface DTFPricingConfig {
  isActive: boolean
  lastUpdated?: string
  updatedBy?: string
  pricingModel: 'per_square_inch' | 'per_linear_inch' | 'flat_rate_by_size' | 'tiered_pricing'
  measurementUnit: 'inches' | 'centimeters' | 'millimeters'
  currency: 'INR' | 'USD' | 'EUR'
  basePricing: {
    minimumOrderValue?: number
    setupFee?: number
    rushOrderMultiplier?: number
  }
  sizeTiers: DTFSizeTier[]
  quantityDiscounts: DTFQuantityDiscount[]
  materialOptions: DTFMaterialOption[]
  additionalServices: DTFAdditionalService[]
  paymentTerms: {
    defaultTerms: 'full_upfront' | 'split_payment' | 'custom_split'
    minimumAdvancePercentage?: number
  }
}
```

### Size Tier Structure
```typescript
interface DTFSizeTier {
  tierName: string
  dimensions: {
    minWidth: number
    maxWidth?: number
    minHeight: number
    maxHeight?: number
  }
  pricing: {
    basePrice: number
    pricePerUnit?: number
  }
  isActive: boolean
}
```

## API Endpoints

### Global Configuration
- **GET** `/api/globals/dtf-pricing` - Fetch pricing configuration
- **POST** `/api/globals/dtf-pricing` - Update pricing configuration

### Quote Calculation (Future Implementation)
- **POST** `/api/dtf-pricing/quote` - Calculate quote based on requirements

## Navigation Integration

The DTF Pricing management is integrated into the admin navigation:
- **Location**: Products section
- **Position**: After Custom T-shirts and Solid T-shirts
- **Access**: Admin users only
- **URL**: `/admin/dtf-pricing`

## Security & Access Control

### Admin-Only Access
- Only users with `role: 'admin'` can access DTF pricing management
- Global configuration uses `adminOnly` access control
- Navigation component checks user role before rendering

### Data Validation
- Form validation for all numeric inputs
- Required field validation
- Logical validation (min/max ranges)

## Future Enhancements (Planned)

### 1. Quantity Discounts
- Bulk order discount configuration
- Percentage, fixed amount, or fixed price discounts
- Quantity range-based pricing

### 2. Material Options
- Different material types (Standard, Premium, Eco-friendly)
- Price adjustments per material
- Default material selection

### 3. Additional Services
- Extra services (lamination, cutting, special finishes)
- Per-item, per-order, or percentage-based pricing
- Service bundling options

### 4. Quote Generation API
- Real-time quote calculation
- Customer-specific pricing
- Quote validity periods
- PDF quote generation

### 5. Advanced Features
- Customer-specific pricing tiers
- Volume-based annual contracts
- Seasonal pricing adjustments
- Multi-location pricing

## Usage Instructions

### Accessing DTF Pricing Management
1. Login to admin panel as admin user
2. Navigate to Products section
3. Click "DTF Printing" in the navigation
4. Configure pricing settings in the tabbed interface

### Configuring Size Tiers
1. Go to "Size Tiers" tab
2. Click "Add Size Tier" to create new tier
3. Fill in tier name and dimensions
4. Set base price and optional price per unit
5. Enable/disable tier as needed
6. Save configuration

### Setting Base Pricing
1. Go to "Overview" tab
2. Configure pricing model and measurement unit
3. Set minimum order value and setup fee
4. Adjust rush order multiplier
5. Configure payment terms
6. Save changes

## Technical Implementation Details

### File Structure
```
src/
├── components/
│   ├── DTFPricingAdmin/index.tsx     # Main admin interface
│   └── DTFPricingNav/index.tsx       # Navigation component
├── globals/
│   └── DTFPricing.ts                 # Payload global configuration
├── types/
│   └── dtf-pricing.ts                # TypeScript interfaces
├── app/
│   ├── (payload)/admin/dtf-pricing/
│   │   └── page.tsx                  # Admin page route
│   └── api/dtf-pricing/
│       └── route.ts                  # API endpoints
└── docs/
    └── DTF_PRICING_SYSTEM.md         # This documentation
```

### Dependencies
- **Payload CMS**: Global configuration and admin UI
- **React**: Component framework
- **TypeScript**: Type safety and interfaces
- **Next.js**: Routing and API endpoints

## Testing

### Manual Testing Checklist
- [ ] Admin access control works correctly
- [ ] Navigation appears in Products section
- [ ] DTF Pricing page loads without errors
- [ ] Size tier CRUD operations work
- [ ] Form validation functions properly
- [ ] Configuration saves and persists
- [ ] Global data structure is correct

### API Testing
- [ ] GET `/api/globals/dtf-pricing` returns configuration
- [ ] POST `/api/globals/dtf-pricing` updates configuration
- [ ] Error handling works for invalid data
- [ ] Authentication is enforced

## Troubleshooting

### Common Issues
1. **Navigation not appearing**: Check user role and component registration
2. **Page not loading**: Verify route configuration and component imports
3. **Save errors**: Check API endpoints and data validation
4. **Type errors**: Ensure TypeScript interfaces are up to date

### Debug Steps
1. Check browser console for JavaScript errors
2. Verify server logs for API errors
3. Confirm user authentication and permissions
4. Validate data structure against TypeScript interfaces

## Conclusion

The DTF Printing Pricing Management System provides a robust foundation for managing DTF printing pricing in the Maddox Tees B2B platform. The current implementation focuses on core functionality with a clear path for future enhancements. The system is designed to be scalable, maintainable, and user-friendly for admin users.
