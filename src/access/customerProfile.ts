import type { Access } from 'payload'

export const customerProfile: Access = ({ req: { user } }) => {
  // Admins can access all customer records
  if (user && user.role === 'admin') return true

  // Customer users can only access their own customer record
  if (user && user.role === 'customer' && user.customerId) {
    const customerIdValue = typeof user.customerId === 'string' ? user.customerId : user.customerId.id
    
    return {
      id: {
        equals: customerIdValue,
      },
    }
  }

  return false
}
