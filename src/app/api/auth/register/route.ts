import { getPayload } from 'payload'
import config from '@payload-config'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    const {
      email,
      password,
      firstName,
      lastName,
      companyName,
      phone,
      gstin,
      billingAddress,
      shippingAddress,
      customerType = 'retail',
    } = body

    // Validate required fields
    if (!email || !password || !firstName || !lastName || !companyName || !phone) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate billing address
    if (!billingAddress?.line1 || !billingAddress?.city || !billingAddress?.state || !billingAddress?.postalCode) {
      return NextResponse.json(
        { error: 'Complete billing address is required' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUsers = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: email,
        },
      },
    })

    if (existingUsers.docs.length > 0) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      )
    }

    // Check if customer already exists
    const existingCustomers = await payload.find({
      collection: 'customers',
      where: {
        email: {
          equals: email,
        },
      },
    })

    if (existingCustomers.docs.length > 0) {
      return NextResponse.json(
        { error: 'Customer with this email already exists' },
        { status: 409 }
      )
    }

    // Create user account first
    const user = await payload.create({
      collection: 'users',
      data: {
        email,
        password,
        name: `${firstName} ${lastName}`,
        role: 'customer',
      },
    })

    // Create customer record
    const customer = await payload.create({
      collection: 'customers',
      data: {
        email,
        firstName,
        lastName,
        companyName,
        phone,
        gstin,
        billingAddress: {
          line1: billingAddress.line1,
          line2: billingAddress.line2 || '',
          city: billingAddress.city,
          state: billingAddress.state,
          postalCode: billingAddress.postalCode,
          country: billingAddress.country || 'India',
        },
        shippingAddress: shippingAddress || {
          sameAsBilling: true,
        },
        customerType,
        status: 'active',
        userId: user.id,
      },
    })

    // Update user with customer relationship
    await payload.update({
      collection: 'users',
      id: user.id,
      data: {
        customerId: customer.id,
      },
    })

    // Return success response (without sensitive data)
    return NextResponse.json({
      success: true,
      message: 'Registration successful! You can now log in to your customer portal.',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
      },
      customer: {
        id: customer.id,
        companyName: customer.companyName,
        customerType: customer.customerType,
      },
    })

  } catch (error) {
    console.error('Registration error:', error)
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('duplicate key')) {
        return NextResponse.json(
          { error: 'An account with this email already exists' },
          { status: 409 }
        )
      }
      
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Registration failed. Please try again.' },
      { status: 500 }
    )
  }
}
