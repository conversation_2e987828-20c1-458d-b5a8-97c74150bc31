import { getPayload } from 'payload'
import config from '@payload-config'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Get the current user from the request
    const authResult = await payload.auth({ headers: request.headers })
    
    return NextResponse.json({
      success: true,
      user: authResult.user ? {
        id: authResult.user.id,
        email: authResult.user.email,
        role: authResult.user.role,
        name: authResult.user.name,
        customerId: authResult.user.customerId,
        createdAt: authResult.user.createdAt,
        updatedAt: authResult.user.updatedAt,
      } : null,
      isAuthenticated: !!authResult.user,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Debug user error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 })
  }
}
