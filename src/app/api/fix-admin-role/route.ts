import { getPayload } from 'payload'
import config from '@payload-config'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Get the current user from the request
    const authResult = await payload.auth({ headers: request.headers })
    
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    // Check if the current user <NAME_EMAIL> user
    if (authResult.user.email !== '<EMAIL>') {
      return NextResponse.json(
        { error: 'This endpoint is only for <NAME_EMAIL> user role' },
        { status: 403 }
      )
    }

    // Update the user's role to admin
    const updatedUser = await payload.update({
      collection: 'users',
      id: authResult.user.id,
      data: {
        role: 'admin',
      },
    })

    return NextResponse.json({
      success: true,
      message: 'User role updated to admin successfully',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role,
        name: updatedUser.name,
      },
    })

  } catch (error) {
    console.error('Fix admin role error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 })
  }
}
