import { NextRequest, NextResponse } from 'next/server'
import { getPayloadClient } from '@/utilities/payload'
import { createShiprocketOrder, generateShiprocketAWB } from '@/utilities/shiprocket'

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { orderId } = body

    if (!orderId) {
      return NextResponse.json({ error: 'Order ID is required' }, { status: 400 })
    }

    // Get the Payload client
    const payload = await getPayloadClient()

    // Get the order from Payload
    const order = await payload.findByID({
      collection: 'orders',
      id: orderId,
    })

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 })
    }

    // Get the customer from Payload
    const customer = await payload.findByID({
      collection: 'customers',
      id: order.customer as string,
    })

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Prepare order items for Shiprocket based on service type
    let orderItems: Array<{
      name: string
      sku: string
      units: number
      selling_price: number
      discount: number
      tax: number
      hsn: number
    }> = []

    if (order.orderType === 'dtf-stickers' && order.serviceSpecificData?.dtfStickers) {
      const dtfData = order.serviceSpecificData.dtfStickers
      orderItems = [
        {
          name: 'DTF Stickers',
          sku: 'DTF-STICKERS',
          units: dtfData.quantity || 1,
          selling_price: dtfData.pricePerUnit || 0,
          discount: 0,
          tax: 0,
          hsn: 4911, // HSN code for printed matter
        },
      ]
    } else if (order.orderType === 'solid-tshirts' && order.serviceSpecificData?.solidTshirts) {
      const solidTshirts = order.serviceSpecificData.solidTshirts
      orderItems = solidTshirts.map((item) => ({
        name: `T-shirt - ${item.size} ${item.color}`,
        sku:
          typeof item.productVariant === 'string'
            ? item.productVariant
            : item.productVariant?.id || 'TSHIRT',
        units: item.quantity || 1,
        selling_price: item.unitPrice || 0,
        discount: 0,
        tax: 0,
        hsn: 6109, // HSN code for t-shirts
      }))
    } else if (order.orderType === 'custom-tshirts' && order.serviceSpecificData?.customTshirts) {
      const customData = order.serviceSpecificData.customTshirts
      const totalAmount = customData.advanceAmount + customData.remainingAmount
      orderItems = [
        {
          name: `Custom T-shirt - ${customData.tshirtSelection?.size} ${customData.tshirtSelection?.color}`,
          sku: 'CUSTOM-TSHIRT',
          units: customData.quantity || 1,
          selling_price: totalAmount / (customData.quantity || 1),
          discount: 0,
          tax: 0,
          hsn: 6109, // HSN code for t-shirts
        },
      ]
    } else {
      // Fallback for legacy orders or unknown types
      orderItems = [
        {
          name: 'Product',
          sku: 'PRODUCT',
          units: 1,
          selling_price: order.subtotal || 0,
          discount: 0,
          tax: 0,
          hsn: 6109,
        },
      ]
    }

    // Determine if shipping is the same as billing
    const shippingIsBilling = customer.shippingAddress?.sameAsBilling || false

    // Create Shiprocket order
    const shiprocketOrderData = {
      order_id: order.orderNumber,
      order_date: order.createdAt
        ? new Date(order.createdAt).toISOString().split('T')[0]
        : new Date().toISOString().split('T')[0],
      pickup_location: 'Primary',
      channel_id: '',
      comment: order.customerNotes || '',
      billing_customer_name: customer.firstName,
      billing_last_name: customer.lastName,
      billing_address: customer.billingAddress.line1,
      billing_address_2: customer.billingAddress.line2 || '',
      billing_city: customer.billingAddress.city,
      billing_pincode: customer.billingAddress.postalCode,
      billing_state: customer.billingAddress.state,
      billing_country: customer.billingAddress.country,
      billing_email: customer.email,
      billing_phone: customer.phone,
      shipping_is_billing: shippingIsBilling,
      shipping_customer_name: shippingIsBilling ? undefined : customer.firstName,
      shipping_last_name: shippingIsBilling ? undefined : customer.lastName,
      shipping_address: shippingIsBilling ? undefined : customer.shippingAddress?.line1,
      shipping_address_2: shippingIsBilling ? undefined : customer.shippingAddress?.line2 || '',
      shipping_city: shippingIsBilling ? undefined : customer.shippingAddress?.city,
      shipping_pincode: shippingIsBilling ? undefined : customer.shippingAddress?.postalCode,
      shipping_state: shippingIsBilling ? undefined : customer.shippingAddress?.state,
      shipping_country: shippingIsBilling ? undefined : customer.shippingAddress?.country,
      shipping_email: shippingIsBilling ? undefined : customer.email,
      shipping_phone: shippingIsBilling ? undefined : customer.phone,
      order_items: orderItems,
      payment_method: 'prepaid', // Only prepaid orders as per requirements
      shipping_charges: order.shipping,
      giftwrap_charges: 0,
      transaction_charges: 0,
      total_discount: order.discount,
      sub_total: order.subtotal,
      length: 10,
      breadth: 10,
      height: 5,
      weight: 0.5,
    }

    // Create Shiprocket order
    const shiprocketResponse = await createShiprocketOrder(shiprocketOrderData)

    // Extract shipment_id and courier_company_id with type checking
    const shipmentId =
      typeof shiprocketResponse.shipment_id === 'string'
        ? shiprocketResponse.shipment_id
        : String(shiprocketResponse.shipment_id || '')

    const courierId =
      typeof shiprocketResponse.courier_company_id === 'string'
        ? shiprocketResponse.courier_company_id
        : String(shiprocketResponse.courier_company_id || '')

    // Generate AWB (tracking number)
    const awbResponse = await generateShiprocketAWB(shipmentId, courierId)

    // Extract tracking details with type checking
    const trackingNumber =
      typeof awbResponse.awb_code === 'string'
        ? awbResponse.awb_code
        : String(awbResponse.awb_code || '')

    const carrier =
      typeof shiprocketResponse.courier_name === 'string'
        ? shiprocketResponse.courier_name
        : String(shiprocketResponse.courier_name || '')

    const shiprocketOrderId =
      typeof shiprocketResponse.order_id === 'string'
        ? shiprocketResponse.order_id
        : String(shiprocketResponse.order_id || '')

    // Update order in Payload with Shiprocket details
    await payload.update({
      collection: 'orders',
      id: orderId,
      data: {
        status: 'shipped',
        shippingDetails: {
          ...order.shippingDetails,
          trackingNumber: trackingNumber,
          carrier: carrier,
          shiprocketOrderId: shiprocketOrderId,
        },
      },
    })

    return NextResponse.json({
      success: true,
      shiprocketOrderId: shiprocketOrderId,
      trackingNumber: trackingNumber,
      carrier: carrier,
    })
  } catch (error) {
    console.error('Shipping error:', error)
    return NextResponse.json({ error: 'Error creating shipment' }, { status: 500 })
  }
}
