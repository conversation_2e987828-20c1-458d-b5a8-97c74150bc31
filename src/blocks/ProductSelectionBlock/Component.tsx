'use client'

import React, { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Package, Shirt, Palette, Eye, AlertCircle } from 'lucide-react'

// Note: This block is now for admin preview only
// Customer-facing shopping functionality has been moved to a separate application
// Tab components removed as they contained customer-facing shopping functionality

// Define types for the component props
type ProductSelectionBlockProps = {
  blockType: 'product-selection-block'
  heading?: string
  subheading?: string
  enableDTFStickers?: boolean
  enableSolidTshirts?: boolean
  enableCustomTshirts?: boolean
  defaultTab?: 'dtf-stickers' | 'solid-tshirts' | 'custom-tshirts'
  backgroundColor?: string
  textColor?: string
  padding?: string
  disableInnerContainer?: boolean
}

type ServiceTab = 'dtf-stickers' | 'solid-tshirts' | 'custom-tshirts'

export const ProductSelectionBlock: React.FC<ProductSelectionBlockProps> = ({
  heading = 'Our Services (Admin Preview)',
  subheading = 'Preview of our three specialized printing services - Customer interface is in separate application',
  enableDTFStickers = true,
  enableSolidTshirts = true,
  enableCustomTshirts = true,
  defaultTab = 'dtf-stickers',
  backgroundColor,
  textColor,
  padding = 'py-16',
  disableInnerContainer = false,
}) => {
  const [activeTab, setActiveTab] = useState<ServiceTab>(defaultTab)

  const tabs = [
    {
      id: 'dtf-stickers' as ServiceTab,
      label: 'DTF Stickers',
      icon: Package,
      description: 'High-quality vinyl stickers - 100% upfront payment',
      enabled: enableDTFStickers,
      color: 'bg-blue-500',
    },
    {
      id: 'solid-tshirts' as ServiceTab,
      label: 'Solid T-shirts',
      icon: Shirt,
      description: 'Ready-to-wear t-shirts - 100% upfront payment',
      enabled: enableSolidTshirts,
      color: 'bg-green-500',
    },
    {
      id: 'custom-tshirts' as ServiceTab,
      label: 'Custom T-shirts',
      icon: Palette,
      description: 'Personalized designs - 50% advance, 50% on delivery',
      enabled: enableCustomTshirts,
      color: 'bg-purple-500',
    },
  ].filter((tab) => tab.enabled)

  // Simplified admin preview - no cart functionality

  const containerClasses = disableInnerContainer ? '' : 'container mx-auto px-4'
  const sectionStyle = {
    backgroundColor,
    color: textColor,
  }

  return (
    <section className={padding} style={sectionStyle}>
      <div className={containerClasses}>
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">{heading}</h2>
          {subheading && <p className="text-lg text-gray-600 max-w-2xl mx-auto">{subheading}</p>}
        </div>

        {/* Service Tabs */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Tab Navigation */}
          <div className="lg:w-80">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Services
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <Button
                      key={tab.id}
                      variant={activeTab === tab.id ? 'default' : 'outline'}
                      className="w-full justify-start h-auto p-4"
                      onClick={() => setActiveTab(tab.id)}
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-10 h-10 rounded-lg ${tab.color} flex items-center justify-center`}
                        >
                          <Icon className="h-5 w-5 text-white" />
                        </div>
                        <div className="text-left">
                          <div className="font-semibold">{tab.label}</div>
                          <div className="text-xs text-gray-500">{tab.description}</div>
                        </div>
                      </div>
                    </Button>
                  )
                })}
              </CardContent>
            </Card>

            {/* Admin Notice */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-orange-500" />
                  Admin Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-gray-600">
                  <p className="mb-2">This is a preview of our services for admin purposes.</p>
                  <p>Customer shopping functionality has been moved to a separate application.</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tab Content */}
          <div className="flex-1">
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <Eye className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">
                    {tabs.find((tab) => tab.id === activeTab)?.label} Preview
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {tabs.find((tab) => tab.id === activeTab)?.description}
                  </p>
                  <Badge variant="outline" className="text-sm">
                    Admin Preview Only - Customer interface is in separate application
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
