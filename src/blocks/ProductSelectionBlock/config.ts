import type { Block } from 'payload'

export const ProductSelectionBlock: Block = {
  slug: 'product-selection-block',
  labels: {
    singular: 'Product Selection Block',
    plural: 'Product Selection Blocks',
  },
  fields: [
    {
      name: 'heading',
      type: 'text',
      required: true,
      defaultValue: 'Select Your Service',
    },
    {
      name: 'subheading',
      type: 'text',
      defaultValue: 'Choose from our three specialized printing services',
    },
    {
      name: 'enableDTFStickers',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Enable DTF Stickers service tab',
      },
    },
    {
      name: 'enableSolidTshirts',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Enable Solid T-shirts service tab',
      },
    },
    {
      name: 'enableCustomTshirts',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Enable Custom T-shirts service tab',
      },
    },
    {
      name: 'defaultTab',
      type: 'select',
      options: [
        { label: 'DTF Stickers', value: 'dtf-stickers' },
        { label: 'Solid T-shirts', value: 'solid-tshirts' },
        { label: 'Custom T-shirts', value: 'custom-tshirts' },
      ],
      defaultValue: 'dtf-stickers',
      admin: {
        description: 'Default active tab when the block loads',
      },
    },

    {
      name: 'backgroundColor',
      type: 'select',
      options: [
        { label: 'Light', value: 'light' },
        { label: 'Dark', value: 'dark' },
        { label: 'Primary', value: 'primary' },
        { label: 'Secondary', value: 'secondary' },
        { label: 'Transparent', value: 'transparent' },
      ],
      defaultValue: 'light',
    },
    {
      name: 'textColor',
      type: 'select',
      options: [
        { label: 'Light', value: 'light' },
        { label: 'Dark', value: 'dark' },
      ],
      defaultValue: 'dark',
      admin: {
        condition: (data, siblingData) => siblingData?.backgroundColor !== 'transparent',
      },
    },
    {
      name: 'padding',
      type: 'select',
      options: [
        { label: 'None', value: 'none' },
        { label: 'Small', value: 'small' },
        { label: 'Medium', value: 'medium' },
        { label: 'Large', value: 'large' },
      ],
      defaultValue: 'medium',
    },
  ],
}
