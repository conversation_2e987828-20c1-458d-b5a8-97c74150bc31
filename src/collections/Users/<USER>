import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'

export const Users: CollectionConfig = {
  slug: 'users',
  access: {
    admin: authenticated,
    create: ({ req: { user } }) => {
      // Allow admins to create users directly
      // Allow unauthenticated requests for customer registration
      return Boolean(user && user.role === 'admin') || Boolean(!user)
    },
    delete: ({ req: { user } }) => {
      // Prevent deletion of customer users for data protection
      // Only allow admin deletion of admin users (if needed)
      return <PERSON><PERSON><PERSON>(user && user.role === 'admin')
    },
    read: authenticated, // Authenticated users can read their own data
    update: ({ req: { user } }) => {
      // Users can update their own profile, admins can update any
      if (user && user.role === 'admin') return true
      return {
        id: {
          equals: user?.id,
        },
      }
    },
  },
  admin: {
    defaultColumns: ['name', 'email', 'role'],
    useAsTitle: 'name',
    group: 'User Management',
  },
  auth: true,
  fields: [
    {
      name: 'name',
      type: 'text',
    },
    {
      name: 'role',
      type: 'select',
      required: true,
      defaultValue: 'customer',
      options: [
        {
          label: 'Admin',
          value: 'admin',
        },
        {
          label: 'Customer',
          value: 'customer',
        },
      ],
      admin: {
        description: 'User role determines access permissions',
      },
      access: {
        update: ({ req: { user } }) => {
          // Only admins can change user roles
          return Boolean(user && user.role === 'admin')
        },
      },
    },
    {
      name: 'customerId',
      type: 'relationship',
      relationTo: 'customers',
      admin: {
        condition: (data) => data?.role === 'customer',
        description: 'Link to customer record for customer users',
      },
    },
  ],
  hooks: {
    beforeChange: [
      async ({ data, req, operation }) => {
        // Prevent creation of new admin users (except during registration flow)
        if (operation === 'create' && data.role === 'admin' && req.user) {
          throw new Error(
            'New admin user creation is disabled. Only customer accounts can be created.',
          )
        }

        // Force customer role for new registrations
        if (operation === 'create' && !req.user) {
          data.role = 'customer'
        }

        return data
      },
    ],
    beforeDelete: [
      async ({ req, id }) => {
        // Get the user being deleted
        const userToDelete = await req.payload.findByID({
          collection: 'users',
          id,
        })

        // Prevent deletion of customer users for data protection
        if (userToDelete && userToDelete.role === 'customer') {
          throw new Error(
            'Customer user accounts cannot be deleted for data protection and audit compliance.',
          )
        }

        return true
      },
    ],
  },
  timestamps: true,
}
