import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { adminOnly } from '../../access/adminOnly'

export const Users: CollectionConfig = {
  slug: 'users',
  access: {
    admin: authenticated,
    create: adminOnly, // Only admins can create users directly
    delete: adminOnly, // Only admins can delete users
    read: authenticated, // Authenticated users can read their own data
    update: ({ req: { user } }) => {
      // Users can update their own profile, admins can update any
      if (user && user.role === 'admin') return true
      return {
        id: {
          equals: user?.id,
        },
      }
    },
  },
  admin: {
    defaultColumns: ['name', 'email', 'role'],
    useAsTitle: 'name',
    group: 'User Management',
  },
  auth: true,
  fields: [
    {
      name: 'name',
      type: 'text',
    },
    {
      name: 'role',
      type: 'select',
      required: true,
      defaultValue: 'admin',
      options: [
        {
          label: 'Admin',
          value: 'admin',
        },
        {
          label: 'Customer',
          value: 'customer',
        },
      ],
      admin: {
        description: 'User role determines access permissions',
      },
    },
    {
      name: 'customerId',
      type: 'relationship',
      relationTo: 'customers',
      admin: {
        condition: (data) => data?.role === 'customer',
        description: 'Link to customer record for customer users',
      },
    },
  ],
  timestamps: true,
}
