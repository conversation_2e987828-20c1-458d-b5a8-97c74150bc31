'use client'

import React from 'react'
import Link from 'next/link'
import { useAuth } from '@payloadcms/ui'

const AdminDashboard: React.FC = () => {
  const { user } = useAuth()

  // Only show for admin users
  if (!user || user.role !== 'admin') {
    return null
  }

  return (
    <div className="admin-dashboard">
      <style jsx>{`
        .admin-dashboard {
          max-width: 1200px;
          margin: 0 auto;
          padding: 2rem;
        }
        .dashboard-header {
          text-align: center;
          margin-bottom: 3rem;
        }
        .dashboard-title {
          font-size: 2.5rem;
          font-weight: 700;
          color: #212529;
          margin-bottom: 0.5rem;
        }
        .dashboard-subtitle {
          font-size: 1.2rem;
          color: #6c757d;
        }
        .dashboard-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
          margin-bottom: 2rem;
        }
        .dashboard-card {
          background: #fff;
          border: 1px solid #e9ecef;
          border-radius: 12px;
          padding: 2rem;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .dashboard-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }
        .card-title {
          font-size: 1.3rem;
          font-weight: 600;
          color: #495057;
          margin-bottom: 1rem;
          display: flex;
          align-items: center;
        }
        .card-icon {
          width: 24px;
          height: 24px;
          margin-right: 0.5rem;
          background: #007bff;
          border-radius: 4px;
          display: inline-block;
        }
        .card-description {
          color: #6c757d;
          line-height: 1.6;
          margin-bottom: 1.5rem;
        }
        .card-link {
          display: inline-block;
          background: #007bff;
          color: white;
          padding: 0.75rem 1.5rem;
          border-radius: 6px;
          text-decoration: none;
          font-weight: 500;
          transition: background-color 0.2s;
        }
        .card-link:hover {
          background: #0056b3;
          color: white;
        }
        .stats-section {
          background: #f8f9fa;
          border-radius: 12px;
          padding: 2rem;
          margin-top: 2rem;
        }
        .stats-title {
          font-size: 1.4rem;
          font-weight: 600;
          color: #495057;
          margin-bottom: 1rem;
        }
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1rem;
        }
        .stat-item {
          text-align: center;
          padding: 1rem;
          background: white;
          border-radius: 8px;
          border: 1px solid #e9ecef;
        }
        .stat-number {
          font-size: 2rem;
          font-weight: 700;
          color: #007bff;
          margin-bottom: 0.5rem;
        }
        .stat-label {
          color: #6c757d;
          font-size: 0.9rem;
        }
      `}</style>

      <div className="dashboard-header">
        <h1 className="dashboard-title">Admin Dashboard</h1>
        <p className="dashboard-subtitle">Welcome back, {user.name || user.email}</p>
      </div>

      <div className="dashboard-grid">
        <div className="dashboard-card">
          <h2 className="card-title">
            <span className="card-icon"></span>
            Customer Management
          </h2>
          <p className="card-description">
            Manage customer accounts, view customer information, and handle customer registrations. 
            Monitor customer activity and maintain customer relationships.
          </p>
          <Link href="/admin/collections/customers" className="card-link">
            Manage Customers
          </Link>
        </div>

        <div className="dashboard-card">
          <h2 className="card-title">
            <span className="card-icon"></span>
            Order Management
          </h2>
          <p className="card-description">
            Process orders, update order status, and track order fulfillment. 
            Handle DTF stickers, custom t-shirts, and solid t-shirt orders.
          </p>
          <Link href="/admin/collections/orders" className="card-link">
            Manage Orders
          </Link>
        </div>

        <div className="dashboard-card">
          <h2 className="card-title">
            <span className="card-icon"></span>
            Product Catalog
          </h2>
          <p className="card-description">
            Manage your product inventory, update product information, and organize 
            product categories for DTF stickers and t-shirts.
          </p>
          <Link href="/admin/collections/products" className="card-link">
            Manage Products
          </Link>
        </div>

        <div className="dashboard-card">
          <h2 className="card-title">
            <span className="card-icon"></span>
            DTF Pricing
          </h2>
          <p className="card-description">
            Configure DTF printing pricing tiers, manage pricing models, and set up 
            quantity-based discounts for your DTF printing services.
          </p>
          <Link href="/admin/globals/dtf-pricing" className="card-link">
            Manage Pricing
          </Link>
        </div>

        <div className="dashboard-card">
          <h2 className="card-title">
            <span className="card-icon"></span>
            Content Management
          </h2>
          <p className="card-description">
            Manage website content, update pages, create blog posts, and handle 
            media uploads for your B2B platform.
          </p>
          <Link href="/admin/collections/pages" className="card-link">
            Manage Content
          </Link>
        </div>

        <div className="dashboard-card">
          <h2 className="card-title">
            <span className="card-icon"></span>
            User Management
          </h2>
          <p className="card-description">
            Manage user accounts, review customer registrations, and handle 
            user permissions and access controls.
          </p>
          <Link href="/admin/collections/users" className="card-link">
            Manage Users
          </Link>
        </div>
      </div>

      <div className="stats-section">
        <h3 className="stats-title">Quick Stats</h3>
        <div className="stats-grid">
          <div className="stat-item">
            <div className="stat-number">-</div>
            <div className="stat-label">Total Customers</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">-</div>
            <div className="stat-label">Pending Orders</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">-</div>
            <div className="stat-label">Products</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">-</div>
            <div className="stat-label">This Month's Revenue</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
