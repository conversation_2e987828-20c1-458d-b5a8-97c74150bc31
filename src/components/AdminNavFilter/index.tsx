'use client'

import React, { useEffect } from 'react'
import { useAuth } from '@payloadcms/ui'

interface AdminNavFilterProps {
  children: React.ReactNode
}

const AdminNavFilter: React.FC<AdminNavFilterProps> = ({ children }) => {
  const { user } = useAuth()

  useEffect(() => {
    // If user is a customer, hide admin navigation
    if (user && user.role === 'customer') {
      const hideAdminNavigation = () => {
        // Wait for navigation to be rendered
        setTimeout(() => {
          // Try multiple selectors for Payload CMS navigation
          const selectors = [
            'a[href*="/admin/collections/"]:not([href*="/admin/collections/orders"])',
            'a[href*="/admin/globals/"]',
            '[data-test-id="nav-item"] a:not([href*="/admin/collections/orders"])',
            '[data-test-id="nav-group"]',
          ]

          selectors.forEach((selector) => {
            const elements = document.querySelectorAll(selector)
            elements.forEach((element) => {
              const href = element.getAttribute('href')
              const parent =
                element.closest('li') ||
                element.closest('[data-test-id="nav-item"]') ||
                element.parentElement

              if (
                href &&
                !href.includes('/admin/collections/orders') &&
                !href.includes('/admin/collections/customers') &&
                parent
              ) {
                ;(parent as HTMLElement).style.display = 'none'
              }
            })
          })

          // Hide navigation groups that don't contain Orders or Customers
          const navGroups = document.querySelectorAll(
            '[data-test-id="nav-group"], .nav-group, .nav__group',
          )
          navGroups.forEach((group) => {
            const hasOrdersLink = group.querySelector('a[href*="/admin/collections/orders"]')
            const hasCustomersLink = group.querySelector('a[href*="/admin/collections/customers"]')
            if (!hasOrdersLink && !hasCustomersLink) {
              ;(group as HTMLElement).style.display = 'none'
            }
          })

          // Rename Customers link to "My Profile" for customer users
          const customersLinks = document.querySelectorAll(
            'a[href*="/admin/collections/customers"]',
          )
          customersLinks.forEach((link) => {
            const linkText = link.textContent
            if (linkText && linkText.includes('Customers')) {
              link.textContent = 'My Profile'
            }
          })

          // Add customer portal styling
          const nav = document.querySelector('nav, [data-test-id="nav"], .nav, .nav__list')
          if (nav && !nav.querySelector('.customer-portal-header')) {
            const header = document.createElement('div')
            header.className = 'customer-portal-header'
            header.innerHTML = 'Customer Portal'
            header.style.cssText = `
              font-weight: 600;
              color: #007bff;
              margin-bottom: 0.5rem;
              padding: 0 1rem;
              font-size: 0.9rem;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              border-top: 3px solid #007bff;
              padding-top: 1rem;
            `
            nav.insertBefore(header, nav.firstChild)
          }
        }, 100)
      }

      hideAdminNavigation()

      // Re-run when navigation changes
      const observer = new MutationObserver(hideAdminNavigation)
      observer.observe(document.body, { childList: true, subtree: true })

      return () => observer.disconnect()
    }
  }, [user])

  return <>{children}</>
}

export default AdminNavFilter
