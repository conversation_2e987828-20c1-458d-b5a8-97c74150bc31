'use client'

import React, { useEffect } from 'react'
import { useAuth } from '@payloadcms/ui'
import { useRouter } from 'next/navigation'

const CustomerProfileRedirect: React.FC = () => {
  const { user } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // If user is a customer, redirect them to their own customer record
    if (user && user.role === 'customer' && user.customerId && typeof window !== 'undefined') {
      const customerIdValue =
        typeof user.customerId === 'string' ? user.customerId : user.customerId.id

      // Check if we're on the customers collection list page
      if (window.location.pathname === '/admin/collections/customers') {
        // Redirect to the customer's own record
        router.push(`/admin/collections/customers/${customerIdValue}`)
      }
    }
  }, [user, router])

  return null // This component doesn't render anything
}

export default CustomerProfileRedirect
