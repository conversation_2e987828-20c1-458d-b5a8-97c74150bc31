'use client'

import React, { useEffect, useState } from 'react'
import { useAuth } from '@payloadcms/ui'

interface CustomerData {
  id: string
  companyName: string
  email: string
  firstName: string
  lastName: string
  phone: string
  gstin?: string
  billingAddress: {
    line1: string
    line2?: string
    city: string
    state: string
    postalCode: string
    country: string
  }
  shippingAddress?: {
    sameAsBilling?: boolean
    line1?: string
    line2?: string
    city?: string
    state?: string
    postalCode?: string
    country?: string
  }
}

const CustomerProfileView: React.FC = () => {
  const { user } = useAuth()
  const [customerData, setCustomerData] = useState<CustomerData | null>(null)
  const [loading, setLoading] = useState(true)
  const [_saving, _setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [isProfilePage, setIsProfilePage] = useState(false)

  useEffect(() => {
    // Check if we're on the customer profile page (client-side only)
    if (typeof window !== 'undefined') {
      setIsProfilePage(window.location.pathname.includes('/admin/collections/customers/'))
    }
  }, [])

  useEffect(() => {
    // Only show for customer users on their profile page
    if (!user || user.role !== 'customer' || !user.customerId) {
      setLoading(false)
      return
    }

    // Only proceed if we're on the profile page
    if (!isProfilePage) {
      setLoading(false)
      return
    }

    // Fetch customer data
    const fetchCustomerData = async () => {
      try {
        const customerIdValue =
          typeof user.customerId === 'string' ? user.customerId : user.customerId.id
        const response = await fetch(`/api/customers/${customerIdValue}`)

        if (response.ok) {
          const data = await response.json()
          setCustomerData(data)
        } else {
          setMessage({ type: 'error', text: 'Failed to load profile data' })
        }
      } catch (_error) {
        setMessage({ type: 'error', text: 'Error loading profile data' })
      } finally {
        setLoading(false)
      }
    }

    fetchCustomerData()
  }, [user, isProfilePage])

  const _handleSave = async (formData: Partial<CustomerData>) => {
    if (!user || !user.customerId || !customerData) return

    _setSaving(true)
    setMessage(null)

    try {
      const customerIdValue =
        typeof user.customerId === 'string' ? user.customerId : user.customerId.id
      const response = await fetch(`/api/customers/${customerIdValue}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const updatedData = await response.json()
        setCustomerData(updatedData)
        setMessage({ type: 'success', text: 'Profile updated successfully!' })
      } else {
        setMessage({ type: 'error', text: 'Failed to update profile' })
      }
    } catch (_error) {
      setMessage({ type: 'error', text: 'Error updating profile' })
    } finally {
      _setSaving(false)
    }
  }

  // Only render for customer users on profile pages
  if (!user || user.role !== 'customer' || !isProfilePage) {
    return null
  }

  if (loading) {
    return (
      <div className="customer-profile-loading">
        <style jsx>{`
          .customer-profile-loading {
            padding: 2rem;
            text-align: center;
            color: #6c757d;
          }
        `}</style>
        Loading your profile...
      </div>
    )
  }

  if (!customerData) {
    return (
      <div className="customer-profile-error">
        <style jsx>{`
          .customer-profile-error {
            padding: 2rem;
            text-align: center;
            color: #dc3545;
          }
        `}</style>
        Unable to load profile data. Please try refreshing the page.
      </div>
    )
  }

  return (
    <div className="customer-profile-view">
      <style jsx>{`
        .customer-profile-view {
          max-width: 800px;
          margin: 0 auto;
          padding: 2rem;
        }
        .profile-header {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 1.5rem;
          margin-bottom: 2rem;
        }
        .profile-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: #212529;
          margin-bottom: 0.5rem;
        }
        .profile-subtitle {
          color: #6c757d;
          margin-bottom: 0;
        }
        .message {
          padding: 0.75rem 1rem;
          border-radius: 4px;
          margin-bottom: 1rem;
        }
        .message.success {
          background: #d4edda;
          border: 1px solid #c3e6cb;
          color: #155724;
        }
        .message.error {
          background: #f8d7da;
          border: 1px solid #f5c6cb;
          color: #721c24;
        }
        .customer-info {
          background: #fff;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 1.5rem;
          margin-bottom: 1rem;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem 0;
          border-bottom: 1px solid #f8f9fa;
        }
        .info-row:last-child {
          border-bottom: none;
        }
        .info-label {
          font-weight: 500;
          color: #495057;
          min-width: 120px;
        }
        .info-value {
          color: #212529;
          flex: 1;
          text-align: right;
        }
        .edit-note {
          background: #e7f3ff;
          border: 1px solid #b3d9ff;
          border-radius: 4px;
          padding: 1rem;
          margin-top: 1rem;
          color: #0056b3;
          font-size: 0.9rem;
        }
      `}</style>

      <div className="profile-header">
        <h1 className="profile-title">My Profile</h1>
        <p className="profile-subtitle">Manage your company information and addresses</p>
      </div>

      {message && <div className={`message ${message.type}`}>{message.text}</div>}

      <div className="customer-info">
        <h3>Company Information</h3>
        <div className="info-row">
          <span className="info-label">Company:</span>
          <span className="info-value">{customerData.companyName}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Contact:</span>
          <span className="info-value">
            {customerData.firstName} {customerData.lastName}
          </span>
        </div>
        <div className="info-row">
          <span className="info-label">Email:</span>
          <span className="info-value">{customerData.email}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Phone:</span>
          <span className="info-value">{customerData.phone}</span>
        </div>
        {customerData.gstin && (
          <div className="info-row">
            <span className="info-label">GSTIN:</span>
            <span className="info-value">{customerData.gstin}</span>
          </div>
        )}
      </div>

      <div className="customer-info">
        <h3>Billing Address</h3>
        <div className="info-row">
          <span className="info-label">Address:</span>
          <span className="info-value">
            {customerData.billingAddress.line1}
            {customerData.billingAddress.line2 && `, ${customerData.billingAddress.line2}`}
          </span>
        </div>
        <div className="info-row">
          <span className="info-label">City:</span>
          <span className="info-value">{customerData.billingAddress.city}</span>
        </div>
        <div className="info-row">
          <span className="info-label">State:</span>
          <span className="info-value">{customerData.billingAddress.state}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Postal Code:</span>
          <span className="info-value">{customerData.billingAddress.postalCode}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Country:</span>
          <span className="info-value">{customerData.billingAddress.country}</span>
        </div>
      </div>

      {customerData.shippingAddress && !customerData.shippingAddress.sameAsBilling && (
        <div className="customer-info">
          <h3>Shipping Address</h3>
          <div className="info-row">
            <span className="info-label">Address:</span>
            <span className="info-value">
              {customerData.shippingAddress.line1}
              {customerData.shippingAddress.line2 && `, ${customerData.shippingAddress.line2}`}
            </span>
          </div>
          <div className="info-row">
            <span className="info-label">City:</span>
            <span className="info-value">{customerData.shippingAddress.city}</span>
          </div>
          <div className="info-row">
            <span className="info-label">State:</span>
            <span className="info-value">{customerData.shippingAddress.state}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Postal Code:</span>
            <span className="info-value">{customerData.shippingAddress.postalCode}</span>
          </div>
          <div className="info-row">
            <span className="info-label">Country:</span>
            <span className="info-value">{customerData.shippingAddress.country}</span>
          </div>
        </div>
      )}

      <div className="edit-note">
        <strong>Note:</strong> To edit your profile information, use the form fields above. You can
        update your company details, contact information, and addresses as needed. Changes will be
        saved automatically when you submit the form.
      </div>
    </div>
  )
}

export default CustomerProfileView
