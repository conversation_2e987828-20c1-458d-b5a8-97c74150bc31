'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@payloadcms/ui'
import { DTFPricingConfig, DTFSizeTier } from '@/types/dtf-pricing'

// Production-ready error boundary (unused in this version)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
class DTFPricingErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('DTF Pricing Admin Error:', error, errorInfo)
  }

  override render() {
    if (this.state.hasError) {
      return (
        <div className="dtf-pricing-error">
          <h2>Something went wrong</h2>
          <p>An error occurred while loading the DTF pricing management interface.</p>
          <details>
            <summary>Error details</summary>
            <pre>{this.state.error?.message}</pre>
          </details>
          <button onClick={() => this.setState({ hasError: false })}>Try again</button>
        </div>
      )
    }

    return this.props.children
  }
}

const DTFPricingAdmin: React.FC = () => {
  const { user } = useAuth()
  const [pricingConfig, setPricingConfig] = useState<DTFPricingConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState<
    'overview' | 'size-tiers' | 'quantity-discounts' | 'materials' | 'services'
  >('overview')
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const fetchPricingConfig = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/globals/dtf-pricing')

      if (response.ok) {
        const data = await response.json()
        setPricingConfig(data)
      } else {
        // If no config exists, create default one
        const defaultConfig: Partial<DTFPricingConfig> = {
          isActive: true,
          pricingModel: 'per_meter',
          measurementUnit: 'meters',
          currency: 'INR',
          basePricing: {
            minimumOrderValue: 500,
            setupFee: 100,
            rushOrderMultiplier: 1.5,
          },
          sizeTiers: [
            {
              tierName: '0-5 meters',
              lengthRange: { minLength: 0, maxLength: 5 },
              pricing: { pricePerMeter: 100 },
              isActive: true,
            },
            {
              tierName: '5-10 meters',
              lengthRange: { minLength: 5, maxLength: 10 },
              pricing: { pricePerMeter: 90 },
              isActive: true,
            },
            {
              tierName: '10+ meters',
              lengthRange: { minLength: 10 },
              pricing: { pricePerMeter: 80 },
              isActive: true,
            },
          ],
          quantityDiscounts: [],
          materialOptions: [],
          additionalServices: [],
          paymentTerms: {
            defaultTerms: 'full_upfront',
            minimumAdvancePercentage: 50,
          },
        }
        setPricingConfig(defaultConfig as DTFPricingConfig)
      }
    } catch (_error) {
      setMessage({ type: 'error', text: 'Failed to load pricing configuration' })
    } finally {
      setLoading(false)
    }
  }

  const savePricingConfig = async () => {
    if (!pricingConfig) return

    try {
      setSaving(true)
      setMessage(null)

      const response = await fetch('/api/globals/dtf-pricing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pricingConfig),
      })

      if (response.ok) {
        const updatedData = await response.json()
        setPricingConfig(updatedData)
        setMessage({ type: 'success', text: 'DTF pricing configuration saved successfully!' })
      } else {
        setMessage({ type: 'error', text: 'Failed to save pricing configuration' })
      }
    } catch (_error) {
      setMessage({ type: 'error', text: 'Error saving pricing configuration' })
    } finally {
      setSaving(false)
    }
  }

  const addSizeTier = () => {
    if (!pricingConfig) return

    const newTier: DTFSizeTier = {
      tierName: '',
      lengthRange: {
        minLength: 0,
        maxLength: 0,
      },
      pricing: {
        pricePerMeter: 0,
      },
      isActive: true,
    }

    setPricingConfig({
      ...pricingConfig,
      sizeTiers: [...pricingConfig.sizeTiers, newTier],
    })
  }

  const removeSizeTier = (index: number) => {
    if (!pricingConfig) return

    const updatedTiers = pricingConfig.sizeTiers.filter((_, i) => i !== index)
    setPricingConfig({
      ...pricingConfig,
      sizeTiers: updatedTiers,
    })
  }

  const updateSizeTier = (index: number, updatedTier: DTFSizeTier) => {
    if (!pricingConfig) return

    const updatedTiers = [...pricingConfig.sizeTiers]
    updatedTiers[index] = updatedTier
    setPricingConfig({
      ...pricingConfig,
      sizeTiers: updatedTiers,
    })
  }

  // useEffect after all function definitions
  useEffect(() => {
    fetchPricingConfig()
  }, [])

  // Check if user has admin access
  if (!user || user.role !== 'admin') {
    return (
      <div className="dtf-pricing-admin">
        <style jsx>{`
          .dtf-pricing-admin {
            padding: 2rem;
            text-align: center;
          }
          .access-denied {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 1rem;
            border-radius: 4px;
          }
        `}</style>
        <div className="access-denied">
          Access denied. Admin privileges required to manage DTF pricing.
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="dtf-pricing-admin">
        <style jsx>{`
          .dtf-pricing-admin {
            padding: 2rem;
            text-align: center;
          }
          .loading {
            color: #6c757d;
          }
        `}</style>
        <div className="loading">Loading DTF pricing configuration...</div>
      </div>
    )
  }

  if (!pricingConfig) {
    return (
      <div className="dtf-pricing-admin">
        <style jsx>{`
          .dtf-pricing-admin {
            padding: 2rem;
            text-align: center;
          }
          .error {
            color: #dc3545;
          }
        `}</style>
        <div className="error">Failed to load pricing configuration</div>
      </div>
    )
  }

  return (
    <div className="dtf-pricing-admin">
      <style jsx>{`
        .dtf-pricing-admin {
          max-width: 1200px;
          margin: 0 auto;
          padding: 2rem;
        }
        .header {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 1.5rem;
          margin-bottom: 2rem;
        }
        .title {
          font-size: 1.75rem;
          font-weight: 600;
          color: #212529;
          margin-bottom: 0.5rem;
        }
        .subtitle {
          color: #6c757d;
          margin-bottom: 1rem;
        }
        .status-toggle {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }
        .tabs {
          display: flex;
          border-bottom: 2px solid #e9ecef;
          margin-bottom: 2rem;
          gap: 0;
        }
        .tab {
          padding: 0.75rem 1.5rem;
          background: none;
          border: none;
          border-bottom: 2px solid transparent;
          cursor: pointer;
          font-weight: 500;
          color: #6c757d;
          transition: all 0.2s;
        }
        .tab:hover {
          color: #495057;
          background: #f8f9fa;
        }
        .tab.active {
          color: #007bff;
          border-bottom-color: #007bff;
          background: #f8f9fa;
        }
        .tab-content {
          background: #fff;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 2rem;
          margin-bottom: 2rem;
        }
        .form-group {
          margin-bottom: 1.5rem;
        }
        .form-label {
          display: block;
          font-weight: 500;
          color: #495057;
          margin-bottom: 0.5rem;
        }
        .form-control {
          width: 100%;
          padding: 0.5rem 0.75rem;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 1rem;
        }
        .form-control:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        .form-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1rem;
        }
        .form-row-3 {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 1rem;
        }
        .btn {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.2s;
        }
        .btn-primary {
          background: #007bff;
          color: white;
        }
        .btn-primary:hover {
          background: #0056b3;
        }
        .btn-success {
          background: #28a745;
          color: white;
        }
        .btn-success:hover {
          background: #1e7e34;
        }
        .btn-danger {
          background: #dc3545;
          color: white;
        }
        .btn-danger:hover {
          background: #c82333;
        }
        .btn-secondary {
          background: #6c757d;
          color: white;
        }
        .btn-secondary:hover {
          background: #545b62;
        }
        .message {
          padding: 0.75rem 1rem;
          border-radius: 4px;
          margin-bottom: 1rem;
        }
        .message.success {
          background: #d4edda;
          border: 1px solid #c3e6cb;
          color: #155724;
        }
        .message.error {
          background: #f8d7da;
          border: 1px solid #f5c6cb;
          color: #721c24;
        }
        .actions {
          display: flex;
          gap: 1rem;
          justify-content: flex-end;
          padding-top: 1rem;
          border-top: 1px solid #e9ecef;
        }
        .tier-card {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 1.5rem;
          margin-bottom: 1rem;
        }
        .tier-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }
        .tier-title {
          font-weight: 600;
          color: #495057;
        }
        .empty-state {
          text-align: center;
          padding: 3rem;
          color: #6c757d;
        }
        .empty-state h3 {
          margin-bottom: 0.5rem;
        }
        .empty-state p {
          margin-bottom: 1.5rem;
        }
      `}</style>

      <div className="header">
        <h1 className="title">DTF Printing Pricing Management</h1>
        <p className="subtitle">
          Configure pricing tiers, discounts, and options for DTF printing services
        </p>
        <div className="status-toggle">
          <input
            type="checkbox"
            id="isActive"
            checked={pricingConfig.isActive}
            onChange={(e) => setPricingConfig({ ...pricingConfig, isActive: e.target.checked })}
          />
          <label htmlFor="isActive">
            {pricingConfig.isActive ? 'Pricing System Active' : 'Pricing System Inactive'}
          </label>
        </div>
      </div>

      {message && <div className={`message ${message.type}`}>{message.text}</div>}

      <div className="tabs">
        <button
          className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`tab ${activeTab === 'size-tiers' ? 'active' : ''}`}
          onClick={() => setActiveTab('size-tiers')}
        >
          Size Tiers
        </button>
        <button
          className={`tab ${activeTab === 'quantity-discounts' ? 'active' : ''}`}
          onClick={() => setActiveTab('quantity-discounts')}
        >
          Quantity Discounts
        </button>
        <button
          className={`tab ${activeTab === 'materials' ? 'active' : ''}`}
          onClick={() => setActiveTab('materials')}
        >
          Materials
        </button>
        <button
          className={`tab ${activeTab === 'services' ? 'active' : ''}`}
          onClick={() => setActiveTab('services')}
        >
          Additional Services
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'overview' && (
          <div>
            <h3>Basic Configuration</h3>
            <div className="form-row-3">
              <div className="form-group">
                <label className="form-label">Pricing Model</label>
                <select
                  className="form-control"
                  value={pricingConfig.pricingModel}
                  onChange={(e) =>
                    setPricingConfig({
                      ...pricingConfig,
                      pricingModel: e.target.value as DTFPricingConfig['pricingModel'],
                    })
                  }
                >
                  <option value="per_square_inch">Per Square Inch</option>
                  <option value="per_linear_inch">Per Linear Inch</option>
                  <option value="flat_rate_by_size">Flat Rate by Size</option>
                  <option value="tiered_pricing">Tiered Pricing</option>
                </select>
              </div>
              <div className="form-group">
                <label className="form-label">Measurement Unit</label>
                <select
                  className="form-control"
                  value={pricingConfig.measurementUnit}
                  onChange={(e) =>
                    setPricingConfig({
                      ...pricingConfig,
                      measurementUnit: e.target.value as DTFPricingConfig['measurementUnit'],
                    })
                  }
                >
                  <option value="inches">Inches</option>
                  <option value="centimeters">Centimeters</option>
                  <option value="millimeters">Millimeters</option>
                </select>
              </div>
              <div className="form-group">
                <label className="form-label">Currency</label>
                <select
                  className="form-control"
                  value={pricingConfig.currency}
                  onChange={(e) =>
                    setPricingConfig({
                      ...pricingConfig,
                      currency: e.target.value as DTFPricingConfig['currency'],
                    })
                  }
                >
                  <option value="INR">INR (₹)</option>
                  <option value="USD">USD ($)</option>
                  <option value="EUR">EUR (€)</option>
                </select>
              </div>
            </div>

            <h3>Base Pricing</h3>
            <div className="form-row-3">
              <div className="form-group">
                <label className="form-label">Minimum Order Value</label>
                <input
                  type="number"
                  className="form-control"
                  value={pricingConfig.basePricing.minimumOrderValue || 0}
                  onChange={(e) =>
                    setPricingConfig({
                      ...pricingConfig,
                      basePricing: {
                        ...pricingConfig.basePricing,
                        minimumOrderValue: parseFloat(e.target.value) || 0,
                      },
                    })
                  }
                />
              </div>
              <div className="form-group">
                <label className="form-label">Setup Fee</label>
                <input
                  type="number"
                  className="form-control"
                  value={pricingConfig.basePricing.setupFee || 0}
                  onChange={(e) =>
                    setPricingConfig({
                      ...pricingConfig,
                      basePricing: {
                        ...pricingConfig.basePricing,
                        setupFee: parseFloat(e.target.value) || 0,
                      },
                    })
                  }
                />
              </div>
              <div className="form-group">
                <label className="form-label">Rush Order Multiplier</label>
                <input
                  type="number"
                  step="0.1"
                  className="form-control"
                  value={pricingConfig.basePricing.rushOrderMultiplier || 1.5}
                  onChange={(e) =>
                    setPricingConfig({
                      ...pricingConfig,
                      basePricing: {
                        ...pricingConfig.basePricing,
                        rushOrderMultiplier: parseFloat(e.target.value) || 1.5,
                      },
                    })
                  }
                />
              </div>
            </div>

            <h3>Payment Terms</h3>
            <div className="form-row">
              <div className="form-group">
                <label className="form-label">Default Payment Terms</label>
                <select
                  className="form-control"
                  value={pricingConfig.paymentTerms.defaultTerms}
                  onChange={(e) =>
                    setPricingConfig({
                      ...pricingConfig,
                      paymentTerms: {
                        ...pricingConfig.paymentTerms,
                        defaultTerms: e.target
                          .value as DTFPricingConfig['paymentTerms']['defaultTerms'],
                      },
                    })
                  }
                >
                  <option value="full_upfront">100% Upfront</option>
                  <option value="split_payment">50% Advance, 50% on Delivery</option>
                  <option value="custom_split">Custom Split</option>
                </select>
              </div>
              <div className="form-group">
                <label className="form-label">Minimum Advance Percentage</label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  className="form-control"
                  value={pricingConfig.paymentTerms.minimumAdvancePercentage || 50}
                  onChange={(e) =>
                    setPricingConfig({
                      ...pricingConfig,
                      paymentTerms: {
                        ...pricingConfig.paymentTerms,
                        minimumAdvancePercentage: parseInt(e.target.value) || 50,
                      },
                    })
                  }
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'size-tiers' && (
          <div>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '1.5rem',
              }}
            >
              <h3>Size-Based Pricing Tiers</h3>
              <button className="btn btn-primary" onClick={addSizeTier}>
                Add Size Tier
              </button>
            </div>

            {pricingConfig.sizeTiers.length === 0 ? (
              <div className="empty-state">
                <h3>No Size Tiers Configured</h3>
                <p>Add size tiers to define pricing based on dimensions</p>
                <button className="btn btn-primary" onClick={addSizeTier}>
                  Add Your First Size Tier
                </button>
              </div>
            ) : (
              pricingConfig.sizeTiers.map((tier, index) => (
                <div key={index} className="tier-card">
                  <div className="tier-header">
                    <span className="tier-title">Size Tier {index + 1}</span>
                    <button className="btn btn-danger" onClick={() => removeSizeTier(index)}>
                      Remove
                    </button>
                  </div>
                  <div className="form-group">
                    <label className="form-label">Tier Name</label>
                    <input
                      type="text"
                      className="form-control"
                      value={tier.tierName}
                      placeholder="e.g., Small, Medium, Large"
                      onChange={(e) => updateSizeTier(index, { ...tier, tierName: e.target.value })}
                    />
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label className="form-label">Min Length (meters)</label>
                      <input
                        type="number"
                        className="form-control"
                        value={tier.lengthRange.minLength}
                        onChange={(e) =>
                          updateSizeTier(index, {
                            ...tier,
                            lengthRange: {
                              ...tier.lengthRange,
                              minLength: parseFloat(e.target.value) || 0,
                            },
                          })
                        }
                      />
                    </div>
                    <div className="form-group">
                      <label className="form-label">Max Length (meters) - optional</label>
                      <input
                        type="number"
                        className="form-control"
                        value={tier.lengthRange.maxLength || ''}
                        onChange={(e) =>
                          updateSizeTier(index, {
                            ...tier,
                            lengthRange: {
                              ...tier.lengthRange,
                              maxLength: e.target.value ? parseFloat(e.target.value) : undefined,
                            },
                          })
                        }
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label className="form-label">Price Per Meter (INR)</label>
                      <input
                        type="number"
                        className="form-control"
                        value={tier.pricing.pricePerMeter}
                        onChange={(e) =>
                          updateSizeTier(index, {
                            ...tier,
                            pricing: {
                              ...tier.pricing,
                              pricePerMeter: parseFloat(e.target.value) || 0,
                            },
                          })
                        }
                      />
                    </div>
                  </div>
                  <div className="form-group">
                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <input
                        type="checkbox"
                        checked={tier.isActive}
                        onChange={(e) =>
                          updateSizeTier(index, { ...tier, isActive: e.target.checked })
                        }
                      />
                      Active
                    </label>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {activeTab === 'quantity-discounts' && (
          <div className="empty-state">
            <h3>Quantity Discounts</h3>
            <p>Quantity discount configuration will be implemented in the next iteration</p>
          </div>
        )}

        {activeTab === 'materials' && (
          <div className="empty-state">
            <h3>Material Options</h3>
            <p>Material options configuration will be implemented in the next iteration</p>
          </div>
        )}

        {activeTab === 'services' && (
          <div className="empty-state">
            <h3>Additional Services</h3>
            <p>Additional services configuration will be implemented in the next iteration</p>
          </div>
        )}
      </div>

      <div className="actions">
        <button className="btn btn-secondary" onClick={fetchPricingConfig} disabled={saving}>
          Reset Changes
        </button>
        <button className="btn btn-success" onClick={savePricingConfig} disabled={saving}>
          {saving ? 'Saving...' : 'Save Configuration'}
        </button>
      </div>
    </div>
  )
}

export default DTFPricingAdmin
