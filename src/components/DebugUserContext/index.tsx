'use client'

import React from 'react'
import { useAuth } from '@payloadcms/ui'

const DebugUserContext: React.FC = () => {
  const { user } = useAuth()

  // Only show debug info in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: '#f0f0f0',
      border: '1px solid #ccc',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px',
    }}>
      <strong>Debug: User Context</strong>
      <br />
      <strong>User ID:</strong> {user?.id || 'undefined'}
      <br />
      <strong>Email:</strong> {user?.email || 'undefined'}
      <br />
      <strong>Role:</strong> {user?.role || 'undefined'}
      <br />
      <strong>Name:</strong> {user?.name || 'undefined'}
      <br />
      <strong>Customer ID:</strong> {user?.customerId || 'undefined'}
    </div>
  )
}

export default DebugUserContext
