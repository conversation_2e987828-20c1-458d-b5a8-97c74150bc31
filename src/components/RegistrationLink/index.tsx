'use client'

import React from 'react'
import Link from 'next/link'

const RegistrationLink: React.FC = () => {
  return (
    <div className="registration-link-container">
      <style jsx>{`
        .registration-link-container {
          text-align: center;
          margin-top: 1.5rem;
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e9ecef;
        }
        .registration-title {
          font-size: 1.1rem;
          font-weight: 600;
          color: #495057;
          margin-bottom: 0.5rem;
        }
        .registration-text {
          font-size: 0.9rem;
          color: #6c757d;
          margin-bottom: 1rem;
        }
        .registration-button {
          display: inline-block;
          padding: 0.75rem 1.5rem;
          background: #007bff;
          color: white;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 500;
          transition: background-color 0.2s ease;
        }
        .registration-button:hover {
          background: #0056b3;
          color: white;
          text-decoration: none;
        }
      `}</style>
      
      <div className="registration-title">
        New to <PERSON>?
      </div>
      <div className="registration-text">
        Create your B2B customer account to access our wholesale pricing, track orders, and manage your business profile.
      </div>
      <Link href="/register" className="registration-button">
        Register for Customer Portal
      </Link>
    </div>
  )
}

export default RegistrationLink
