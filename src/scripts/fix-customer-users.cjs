/**
 * <PERSON>ript to fix existing customer users by setting their customerId
 */

const { getPayload } = require('payload')
const config = require('./payload.config.ts').default

async function fixCustomerUsers() {
  const payload = await getPayload({ config })

  console.log('🔧 Fixing existing customer users...\n')

  try {
    // Find all customers that have a linked user account
    const customers = await payload.find({
      collection: 'customers',
      where: {
        userId: {
          exists: true,
        },
      },
    })

    console.log(`Found ${customers.docs.length} customers with linked user accounts`)

    for (const customer of customers.docs) {
      if (customer.userId) {
        try {
          // Update the user account to set the customerId
          await payload.update({
            collection: 'users',
            id: customer.userId,
            data: {
              customerId: customer.id,
            },
          })

          console.log(`✅ Fixed user ${customer.userId} for customer ${customer.email}`)
        } catch (error) {
          console.error(`❌ Failed to fix user for customer ${customer.email}:`, error.message)
        }
      }
    }

    console.log('\n🎉 Customer user fix completed!')
  } catch (error) {
    console.error('❌ Script failed:', error)
  }
}

// Run the script
fixCustomerUsers()
  .then(() => {
    console.log('\n✨ Script completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Script failed:', error)
    process.exit(1)
  })
