/**
 * Test script to verify customer portal functionality
 * This script tests the customer user creation and access controls
 */

const { getPayload } = require('payload')
const config = require('../../payload.config.js').default

async function testCustomerPortal() {
  const payload = await getPayload({ config })

  console.log('🧪 Testing Customer Portal Implementation...\n')

  try {
    // Test 1: Create a test customer with user account
    console.log('1. Creating test customer with user account...')
    const testCustomer = await payload.create({
      collection: 'customers',
      data: {
        companyName: 'Test Company Ltd',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+91-**********',
        billingAddress: {
          line1: '123 Test Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          postalCode: '400001',
          country: 'India',
        },
        customerType: 'retail',
        status: 'active',
        initialPassword: 'TestPassword123!',
      },
    })

    console.log('✅ Customer created successfully:', testCustomer.email)
    console.log('✅ User account linked:', testCustomer.userId ? 'Yes' : 'No')

    // Test 2: Verify user account was created
    if (testCustomer.userId) {
      const linkedUser = await payload.findByID({
        collection: 'users',
        id: typeof testCustomer.userId === 'string' ? testCustomer.userId : testCustomer.userId.id,
      })

      console.log('✅ Linked user account found:', linkedUser.email)
      console.log('✅ User role:', linkedUser.role)
      console.log('✅ Customer ID linked:', linkedUser.customerId ? 'Yes' : 'No')
    }

    console.log('\n🎉 Basic tests passed! Customer portal user creation is working correctly.')

    // Cleanup
    console.log('\n2. Cleaning up test data...')
    if (testCustomer.userId) {
      await payload.delete({
        collection: 'users',
        id: typeof testCustomer.userId === 'string' ? testCustomer.userId : testCustomer.userId.id,
      })
    }
    await payload.delete({
      collection: 'customers',
      id: testCustomer.id,
    })
    console.log('✅ Test data cleaned up')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testCustomerPortal().then(() => {
    console.log('\n✨ Test completed')
    process.exit(0)
  }).catch((error) => {
    console.error('❌ Test script failed:', error)
    process.exit(1)
  })
}

module.exports = { testCustomerPortal }
