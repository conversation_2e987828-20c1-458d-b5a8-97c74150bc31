/**
 * Test script for Customer Registration and Data Protection Features
 * 
 * This script tests:
 * 1. Customer registration API
 * 2. Data protection rules (deletion prevention)
 * 3. User role management
 * 4. Access control verification
 */

const testRegistrationData = {
  email: '<EMAIL>',
  password: 'TestPassword123',
  firstName: '<PERSON>',
  lastName: 'Doe',
  companyName: 'Test Company Ltd',
  phone: '+91-9876543210',
  gstin: '29ABCDE1234F1Z5',
  customerType: 'retail',
  billingAddress: {
    line1: '123 Test Street',
    line2: 'Test Building',
    city: 'Mumbai',
    state: 'Maharashtra',
    postalCode: '400001',
    country: 'India'
  }
}

async function testCustomerRegistration() {
  console.log('🧪 Testing Customer Registration API...')
  
  try {
    const response = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testRegistrationData)
    })

    const data = await response.json()

    if (response.ok) {
      console.log('✅ Registration successful!')
      console.log('User ID:', data.user.id)
      console.log('Customer ID:', data.customer.id)
      console.log('User Role:', data.user.role)
      return { userId: data.user.id, customerId: data.customer.id }
    } else {
      console.log('❌ Registration failed:', data.error)
      return null
    }
  } catch (error) {
    console.log('❌ Network error:', error.message)
    return null
  }
}

async function testDuplicateRegistration() {
  console.log('\n🧪 Testing Duplicate Registration Prevention...')
  
  try {
    const response = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testRegistrationData)
    })

    const data = await response.json()

    if (response.status === 409) {
      console.log('✅ Duplicate registration correctly prevented!')
      console.log('Error message:', data.error)
    } else {
      console.log('❌ Duplicate registration was not prevented')
    }
  } catch (error) {
    console.log('❌ Network error:', error.message)
  }
}

async function testLoginAfterRegistration() {
  console.log('\n🧪 Testing Login After Registration...')
  
  try {
    const response = await fetch('http://localhost:3000/api/users/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testRegistrationData.email,
        password: testRegistrationData.password
      })
    })

    const data = await response.json()

    if (response.ok) {
      console.log('✅ Login successful!')
      console.log('User role:', data.user.role)
      console.log('Token received:', !!data.token)
      return data.token
    } else {
      console.log('❌ Login failed:', data.message || data.error)
      return null
    }
  } catch (error) {
    console.log('❌ Network error:', error.message)
    return null
  }
}

async function testDataProtection(token, userId, customerId) {
  console.log('\n🧪 Testing Data Protection Rules...')
  
  // Test customer deletion prevention
  try {
    const response = await fetch(`http://localhost:3000/api/customers/${customerId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    })

    if (response.status === 403 || response.status === 401) {
      console.log('✅ Customer deletion correctly prevented!')
    } else {
      console.log('❌ Customer deletion was not prevented')
    }
  } catch (error) {
    console.log('❌ Error testing customer deletion:', error.message)
  }

  // Test user deletion prevention
  try {
    const response = await fetch(`http://localhost:3000/api/users/${userId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    })

    if (response.status === 403 || response.status === 401) {
      console.log('✅ Customer user deletion correctly prevented!')
    } else {
      console.log('❌ Customer user deletion was not prevented')
    }
  } catch (error) {
    console.log('❌ Error testing user deletion:', error.message)
  }
}

async function testCustomerPortalAccess(token) {
  console.log('\n🧪 Testing Customer Portal Access...')
  
  try {
    // Test access to own customer data
    const response = await fetch('http://localhost:3000/api/customers', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    })

    const data = await response.json()

    if (response.ok) {
      console.log('✅ Customer can access their data!')
      console.log('Customer records visible:', data.docs.length)
    } else {
      console.log('❌ Customer cannot access their data:', data.message)
    }
  } catch (error) {
    console.log('❌ Error testing customer portal access:', error.message)
  }
}

async function runAllTests() {
  console.log('🚀 Starting Customer Registration and Data Protection Tests\n')
  
  // Test 1: Customer Registration
  const registrationResult = await testCustomerRegistration()
  
  if (!registrationResult) {
    console.log('❌ Registration failed, skipping remaining tests')
    return
  }

  // Test 2: Duplicate Registration Prevention
  await testDuplicateRegistration()

  // Test 3: Login After Registration
  const token = await testLoginAfterRegistration()
  
  if (!token) {
    console.log('❌ Login failed, skipping remaining tests')
    return
  }

  // Test 4: Data Protection Rules
  await testDataProtection(token, registrationResult.userId, registrationResult.customerId)

  // Test 5: Customer Portal Access
  await testCustomerPortalAccess(token)

  console.log('\n🎉 All tests completed!')
  console.log('\n📋 Test Summary:')
  console.log('- Customer registration API')
  console.log('- Duplicate registration prevention')
  console.log('- Login functionality')
  console.log('- Data protection rules')
  console.log('- Customer portal access')
  
  console.log('\n⚠️  Note: To clean up test data, manually remove the test customer from the admin panel')
  console.log('   Email:', testRegistrationData.email)
}

// Run tests if this script is executed directly
if (typeof window === 'undefined') {
  runAllTests().catch(console.error)
}

module.exports = {
  testCustomerRegistration,
  testDuplicateRegistration,
  testLoginAfterRegistration,
  testDataProtection,
  testCustomerPortalAccess,
  runAllTests
}
